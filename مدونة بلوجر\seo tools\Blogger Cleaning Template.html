<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free Blogger Cleaning Template - Optimize & Clean Blogger Code</title>
    <meta name="description" content="Clean and optimize your Blogger template code instantly with our free Blogger Cleaning Template tool. Remove unused CSS, JavaScript, and improve site performance.">
    <meta name="keywords" content="blogger cleaning template, blogger template cleaner, optimize blogger template, clean blogger code, blogger seo optimization">
    <link rel="canonical" href="https://www.webtoolskit.org/p/blogger-cleaning-template.html">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "WebApplication"],
        "name": "Free Blogger Cleaning Template - Optimize & Clean Blogger Code",
        "description": "Clean and optimize your Blogger template code instantly with our free Blogger Cleaning Template tool. Remove unused CSS, JavaScript, and improve site performance.",
        "url": "https://www.webtoolskit.org/p/blogger-cleaning-template.html",
        "isAccessibleForFree": true,
        "datePublished": "2025-06-27",
        "dateModified": "2025-06-27",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Blogger Cleaning Template",
            "applicationCategory": "DeveloperTool",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" },
            "featureList": [
                "Blogger template optimization",
                "Code cleaning",
                "Performance improvement",
                "CSS minification",
                "JavaScript optimization"
            ]
        },
        "potentialAction": [
            { "@type": "CreateAction", "name": "Clean Blogger Template" },
            { "@type": "CopyAction", "name": "Copy Cleaned Code" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How do I clean up my Blogger template code?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Using our Blogger Cleaning Template tool is the easiest way. Simply paste your Blogger template code into the input field above, click 'Clean Template', and the tool will automatically remove unused CSS, optimize JavaScript, clean up HTML structure, and improve overall code quality for better performance."
          }
        },
        {
          "@type": "Question",
          "name": "What is a Blogger template cleaner and why do I need it?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A Blogger template cleaner is a tool that optimizes your blog's template code by removing unnecessary elements, minifying CSS and JavaScript, and improving code structure. You need it to enhance your blog's loading speed, improve SEO rankings, reduce bandwidth usage, and provide a better user experience."
          }
        },
        {
          "@type": "Question",
          "name": "How to optimize Blogger template for better SEO performance?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To optimize your Blogger template for SEO, clean up unnecessary code, minify CSS and JavaScript, optimize images, use semantic HTML structure, add proper meta tags, implement schema markup, and ensure mobile responsiveness. Our tool handles the code optimization part automatically."
          }
        },
        {
          "@type": "Question",
          "name": "Can I remove unused CSS and JavaScript from my Blogger theme?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, our Blogger Cleaning Template tool automatically identifies and removes unused CSS rules and JavaScript code from your theme. This reduces file size, improves loading speed, and enhances overall site performance without affecting functionality."
          }
        },
        {
          "@type": "Question",
          "name": "How to make my Blogger template load faster and cleaner?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To make your Blogger template load faster, use our cleaning tool to remove unused code, minify CSS and JavaScript, optimize images, enable compression, use efficient HTML structure, and eliminate render-blocking resources. Our tool handles most of these optimizations automatically."
          }
        }
      ]
    }
    </script>

    <style>
        /* Blogger Cleaning Template Widget - Simplified & Template Compatible */
        .blogger-cleaning-template-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .blogger-cleaning-template-widget-container * { box-sizing: border-box; }

        .blogger-cleaning-template-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .blogger-cleaning-template-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .blogger-cleaning-template-form {
            display: grid;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .blogger-cleaning-template-field {
            display: flex;
            flex-direction: column;
        }

        .blogger-cleaning-template-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .blogger-cleaning-template-textarea {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: 'SF Mono', Monaco, monospace;
            color: var(--text-color);
            resize: vertical;
            min-height: 200px;
        }

        .blogger-cleaning-template-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .blogger-cleaning-template-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .blogger-cleaning-template-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .blogger-cleaning-template-btn:hover { transform: translateY(-2px); }

        .blogger-cleaning-template-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .blogger-cleaning-template-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .blogger-cleaning-template-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .blogger-cleaning-template-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .blogger-cleaning-template-btn-success {
            background-color: #10b981;
            color: white;
        }

        .blogger-cleaning-template-btn-success:hover {
            background-color: #059669;
        }

        .blogger-cleaning-template-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .blogger-cleaning-template-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .blogger-cleaning-template-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-word;
            min-height: 200px;
            color: var(--text-color);
            line-height: 1.5;
            white-space: pre-wrap;
        }

        .blogger-cleaning-template-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .blogger-cleaning-template-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        @media (max-width: 768px) {
            .blogger-cleaning-template-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .blogger-cleaning-template-widget-title { font-size: 1.875rem; }
            .blogger-cleaning-template-buttons { flex-direction: column; }
            .blogger-cleaning-template-btn { flex: none; }
        }

        [data-theme="dark"] .blogger-cleaning-template-textarea:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .blogger-cleaning-template-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .blogger-cleaning-template-output::selection { background-color: var(--primary-color); color: white; }

        .blogger-cleaning-template-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }

        a[href*="meta-tag-generator"] .blogger-cleaning-template-related-tool-icon { background: linear-gradient(145deg, #2563eb, #1d4ed8); }
        a[href*="robots-txt-generator"] .blogger-cleaning-template-related-tool-icon { background: linear-gradient(145deg, #8B5CF6, #7C3AED); }
        a[href*="sitemap-generator"] .blogger-cleaning-template-related-tool-icon { background: linear-gradient(145deg, #EC4899, #DB2777); }

        .blogger-cleaning-template-related-tool-item:hover .blogger-cleaning-template-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }

        a[href*="meta-tag-generator"]:hover .blogger-cleaning-template-related-tool-icon { background: linear-gradient(145deg, #3b82f6, #2563eb); }
        a[href*="robots-txt-generator"]:hover .blogger-cleaning-template-related-tool-icon { background: linear-gradient(145deg, #9373f7, #8a4ff0); }
        a[href*="sitemap-generator"]:hover .blogger-cleaning-template-related-tool-icon { background: linear-gradient(145deg, #f472b6, #ec4899); }

        .blogger-cleaning-template-related-tool-item { box-shadow: none; border: none; }
        .blogger-cleaning-template-related-tool-item:hover { box-shadow: none; border: none; }
        .blogger-cleaning-template-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .blogger-cleaning-template-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .blogger-cleaning-template-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .blogger-cleaning-template-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .blogger-cleaning-template-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .blogger-cleaning-template-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .blogger-cleaning-template-related-tool-item:hover .blogger-cleaning-template-related-tool-name { color: var(--primary-color); }

        .blogger-cleaning-template-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .blogger-cleaning-template-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .blogger-cleaning-template-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }

        .blogger-cleaning-template-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
            break-inside: avoid;
        }

        .blogger-cleaning-template-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }

        @media (max-width: 768px) {
            .blogger-cleaning-template-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .blogger-cleaning-template-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .blogger-cleaning-template-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .blogger-cleaning-template-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 600px) { .blogger-cleaning-template-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }

        @media (max-width: 480px) {
            .blogger-cleaning-template-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .blogger-cleaning-template-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .blogger-cleaning-template-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .blogger-cleaning-template-related-tool-name { font-size: 0.75rem; }
        }
    </style>
</head>
<body>
    <div class="blogger-cleaning-template-widget-container">
        <h1 class="blogger-cleaning-template-widget-title">Blogger Cleaning Template</h1>
        <p class="blogger-cleaning-template-widget-description">
            Clean and optimize your Blogger template code for better performance and SEO. Remove unused CSS, minify JavaScript, and improve your blog's loading speed.
        </p>

        <form class="blogger-cleaning-template-form">
            <div class="blogger-cleaning-template-field">
                <label for="bloggerTemplateCode" class="blogger-cleaning-template-label">Blogger Template Code:</label>
                <textarea
                    id="bloggerTemplateCode"
                    class="blogger-cleaning-template-textarea"
                    placeholder="Paste your Blogger template XML code here..."
                ></textarea>
            </div>
        </form>

        <div class="blogger-cleaning-template-buttons">
            <button class="blogger-cleaning-template-btn blogger-cleaning-template-btn-primary" onclick="BloggerCleaningTemplate.clean()">
                Clean Template
            </button>
            <button class="blogger-cleaning-template-btn blogger-cleaning-template-btn-secondary" onclick="BloggerCleaningTemplate.clear()">
                Clear All
            </button>
            <button class="blogger-cleaning-template-btn blogger-cleaning-template-btn-success" onclick="BloggerCleaningTemplate.copy()">
                Copy Cleaned Code
            </button>
        </div>

        <div class="blogger-cleaning-template-result">
            <h3 class="blogger-cleaning-template-result-title">Cleaned Template Code:</h3>
            <div class="blogger-cleaning-template-output" id="bloggerCleaningOutput">Your cleaned template code will appear here...</div>
        </div>

        <div class="blogger-cleaning-template-related-tools">
            <h3 class="blogger-cleaning-template-related-tools-title">Related Tools</h3>
            <div class="blogger-cleaning-template-related-tools-grid">
                <a href="/p/meta-tag-generator.html" class="blogger-cleaning-template-related-tool-item" rel="noopener">
                    <div class="blogger-cleaning-template-related-tool-icon">
                        <i class="fas fa-tags"></i>
                    </div>
                    <div class="blogger-cleaning-template-related-tool-name">Meta Tag Generator</div>
                </a>

                <a href="/p/robots-txt-generator.html" class="blogger-cleaning-template-related-tool-item" rel="noopener">
                    <div class="blogger-cleaning-template-related-tool-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="blogger-cleaning-template-related-tool-name">Robots.txt Generator</div>
                </a>

                <a href="/p/sitemap-generator.html" class="blogger-cleaning-template-related-tool-item" rel="noopener">
                    <div class="blogger-cleaning-template-related-tool-icon">
                        <i class="fas fa-sitemap"></i>
                    </div>
                    <div class="blogger-cleaning-template-related-tool-name">Sitemap Generator</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Professional Blogger Cleaning Template for Better Performance</h2>
            <p>Our <strong>Blogger Cleaning Template</strong> tool helps you optimize your Blogger template code for maximum performance and SEO benefits. Clean templates load faster, rank better in search engines, and provide a superior user experience for your blog visitors.</p>
            <p>Whether you're a blogger, web developer, or digital marketer, our tool makes it easy to remove unnecessary code, minify CSS and JavaScript, and optimize your Blogger template following current best practices. Simply paste your template code, and we'll clean it up automatically.</p>

            <h3>How to Use the Blogger Cleaning Template Tool</h3>
            <ol>
                <li><strong>Paste Template Code:</strong> Copy your Blogger template XML code and paste it into the text area above.</li>
                <li><strong>Clean Template:</strong> Click the "Clean Template" button to automatically optimize your code.</li>
                <li><strong>Copy and Implement:</strong> Copy the cleaned code and upload it back to your Blogger template.</li>
            </ol>

            <h3>Frequently Asked Questions About Blogger Template Cleaning</h3>

            <h4>How do I clean up my Blogger template code?</h4>
            <p>Using our Blogger Cleaning Template tool is the easiest way. Simply paste your Blogger template code into the input field above, click 'Clean Template', and the tool will automatically remove unused CSS, optimize JavaScript, clean up HTML structure, and improve overall code quality for better performance.</p>

            <h4>What is a Blogger template cleaner and why do I need it?</h4>
            <p>A Blogger template cleaner is a tool that optimizes your blog's template code by removing unnecessary elements, minifying CSS and JavaScript, and improving code structure. You need it to enhance your blog's loading speed, improve SEO rankings, reduce bandwidth usage, and provide a better user experience.</p>

            <h4>How to optimize Blogger template for better SEO performance?</h4>
            <p>To optimize your Blogger template for SEO, clean up unnecessary code, minify CSS and JavaScript, optimize images, use semantic HTML structure, add proper meta tags, implement schema markup, and ensure mobile responsiveness. Our tool handles the code optimization part automatically.</p>

            <h4>Can I remove unused CSS and JavaScript from my Blogger theme?</h4>
            <p>Yes, our Blogger Cleaning Template tool automatically identifies and removes unused CSS rules and JavaScript code from your theme. This reduces file size, improves loading speed, and enhances overall site performance without affecting functionality.</p>

            <h4>How to make my Blogger template load faster and cleaner?</h4>
            <p>To make your Blogger template load faster, use our cleaning tool to remove unused code, minify CSS and JavaScript, optimize images, enable compression, use efficient HTML structure, and eliminate render-blocking resources. Our tool handles most of these optimizations automatically.</p>
        </div>

        <div class="blogger-cleaning-template-features">
            <h3 class="blogger-cleaning-template-features-title">Key Features:</h3>
            <ul class="blogger-cleaning-template-features-list">
                <li class="blogger-cleaning-template-features-item" style="margin-bottom: 0.3em;">Blogger Template Optimization</li>
                <li class="blogger-cleaning-template-features-item" style="margin-bottom: 0.3em;">CSS Code Minification</li>
                <li class="blogger-cleaning-template-features-item" style="margin-bottom: 0.3em;">JavaScript Optimization</li>
                <li class="blogger-cleaning-template-features-item" style="margin-bottom: 0.3em;">HTML Structure Cleanup</li>
                <li class="blogger-cleaning-template-features-item" style="margin-bottom: 0.3em;">Performance Improvement</li>
                <li class="blogger-cleaning-template-features-item" style="margin-bottom: 0.3em;">SEO-Friendly Code</li>
                <li class="blogger-cleaning-template-features-item">100% Free and Secure</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="blogger-cleaning-template-notification" id="bloggerCleaningNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        (function() {
            'use strict';

            const elements = {
                templateCode: () => document.getElementById('bloggerTemplateCode'),
                output: () => document.getElementById('bloggerCleaningOutput'),
                notification: () => document.getElementById('bloggerCleaningNotification')
            };

            window.BloggerCleaningTemplate = {
                clean() {
                    const templateCode = elements.templateCode().value.trim();
                    const output = elements.output();

                    if (!templateCode) {
                        output.textContent = 'Please paste your Blogger template code to clean it.';
                        output.style.color = '#dc2626';
                        return;
                    }

                    output.style.color = '';

                    try {
                        // Clean the template code
                        let cleanedCode = this.cleanBloggerTemplate(templateCode);
                        output.textContent = cleanedCode;
                    } catch (error) {
                        output.textContent = 'Error: Unable to clean the template code. Please check your input and try again.';
                        output.style.color = '#dc2626';
                    }
                },

                cleanBloggerTemplate(code) {
                    let cleanedCode = code;

                    // Remove comments
                    cleanedCode = cleanedCode.replace(/<!--[\s\S]*?-->/g, '');

                    // Remove extra whitespace and line breaks
                    cleanedCode = cleanedCode.replace(/\s+/g, ' ');
                    cleanedCode = cleanedCode.replace(/>\s+</g, '><');

                    // Clean up CSS
                    cleanedCode = cleanedCode.replace(/<style[^>]*>([\s\S]*?)<\/style>/gi, (match, css) => {
                        let cleanedCSS = css;
                        // Remove CSS comments
                        cleanedCSS = cleanedCSS.replace(/\/\*[\s\S]*?\*\//g, '');
                        // Remove extra spaces
                        cleanedCSS = cleanedCSS.replace(/\s+/g, ' ');
                        // Remove spaces around CSS syntax
                        cleanedCSS = cleanedCSS.replace(/\s*{\s*/g, '{');
                        cleanedCSS = cleanedCSS.replace(/\s*}\s*/g, '}');
                        cleanedCSS = cleanedCSS.replace(/\s*;\s*/g, ';');
                        cleanedCSS = cleanedCSS.replace(/\s*:\s*/g, ':');
                        cleanedCSS = cleanedCSS.replace(/\s*,\s*/g, ',');
                        return `<style>${cleanedCSS.trim()}</style>`;
                    });

                    // Clean up JavaScript
                    cleanedCode = cleanedCode.replace(/<script[^>]*>([\s\S]*?)<\/script>/gi, (match, js) => {
                        let cleanedJS = js;
                        // Remove JS comments (simple approach)
                        cleanedJS = cleanedJS.replace(/\/\/.*$/gm, '');
                        cleanedJS = cleanedJS.replace(/\/\*[\s\S]*?\*\//g, '');
                        // Remove extra spaces
                        cleanedJS = cleanedJS.replace(/\s+/g, ' ');
                        return match.replace(js, cleanedJS.trim());
                    });

                    // Format the output for better readability
                    cleanedCode = this.formatXML(cleanedCode);

                    return cleanedCode;
                },

                formatXML(xml) {
                    const PADDING = '  ';
                    const reg = /(>)(<)(\/*)/g;
                    let formatted = xml.replace(reg, '$1\r\n$2$3');
                    let pad = 0;

                    return formatted.split('\r\n').map((node) => {
                        let indent = 0;
                        if (node.match(/.+<\/\w[^>]*>$/)) {
                            indent = 0;
                        } else if (node.match(/^<\/\w/) && pad > 0) {
                            pad -= 1;
                        } else if (node.match(/^<\w[^>]*[^\/]>.*$/)) {
                            indent = 1;
                        } else {
                            indent = 0;
                        }

                        pad += indent;
                        return PADDING.repeat(pad - indent) + node;
                    }).join('\r\n');
                },

                clear() {
                    elements.templateCode().value = '';
                    elements.output().textContent = 'Your cleaned template code will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.output().textContent;
                    if (text === 'Your cleaned template code will appear here...' || text.startsWith('Please paste') || text.startsWith('Error:')) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            document.addEventListener('DOMContentLoaded', function() {
                // Enter key shortcuts
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        BloggerCleaningTemplate.clean();
                    }
                });
            });
        })();
    </script>
</body>
</html>
